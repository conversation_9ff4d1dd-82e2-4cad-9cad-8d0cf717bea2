package top.yogiczy.mytv.core.data.utils

import org.junit.Test
import org.junit.Assert.*
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import java.text.SimpleDateFormat
import java.util.*

class ChannelUtilTest {

    @Test
    fun testChannelLineSupportPlayback_withCatchupSource() {
        val channelLine = ChannelLine(
            url = "http://example.com/live/channel.m3u8",
            catchupSource = "rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT"
        )
        
        assertTrue("Channel line with catchup-source should support playback", 
                   ChannelUtil.channelLineSupportPlayback(channelLine))
    }

    @Test
    fun testChannelLineSupportPlayback_withPltv() {
        val channelLine = ChannelLine(
            url = "http://example.com/pltv/channel.m3u8"
        )
        
        assertTrue("Channel line with pltv URL should support playback", 
                   ChannelUtil.channelLineSupportPlayback(channelLine))
    }

    @Test
    fun testChannelLineSupportPlayback_withTvod() {
        val channelLine = ChannelLine(
            url = "http://example.com/tvod/channel.m3u8"
        )
        
        assertTrue("Channel line with tvod URL should support playback", 
                   ChannelUtil.channelLineSupportPlayback(channelLine))
    }

    @Test
    fun testChannelLineSupportPlayback_withoutPlaybackSupport() {
        val channelLine = ChannelLine(
            url = "http://example.com/live/channel.m3u8"
        )
        
        assertFalse("Channel line without playback support should return false", 
                    ChannelUtil.channelLineSupportPlayback(channelLine))
    }

    @Test
    fun testBuildCatchupUrl() {
        val catchupSource = "rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT"
        
        // 测试时间：2024年12月18日 14:30:00 到 15:30:00
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.DECEMBER, 18, 14, 30, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startTime = calendar.timeInMillis
        
        calendar.set(Calendar.HOUR_OF_DAY, 15)
        val endTime = calendar.timeInMillis
        
        val result = ChannelUtil.buildCatchupUrl(catchupSource, startTime, endTime)
        val expected = "rtsp://***************:1554/iptv/Tvod/iptv/001/001/ch12122514263996485740.rsc?tvdr=20241218143000GMT-20241218153000GMT"
        
        assertEquals("Catchup URL should be built correctly", expected, result)
    }

    @Test
    fun testBuildCatchupUrl_withDifferentFormat() {
        val catchupSource = "http://example.com/playback?start={utc:YmdHMS}&end={utcend:YmdHMS}"
        
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.DECEMBER, 18, 20, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startTime = calendar.timeInMillis
        
        calendar.set(Calendar.HOUR_OF_DAY, 21)
        val endTime = calendar.timeInMillis
        
        val result = ChannelUtil.buildCatchupUrl(catchupSource, startTime, endTime)
        val expected = "http://example.com/playback?start=20241218200000&end=20241218210000"
        
        assertEquals("Catchup URL with different format should be built correctly", expected, result)
    }
}
